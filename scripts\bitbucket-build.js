#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Easy Voice - Bitbucket Pipelines Build');
console.log('==========================================');

// Environment detection
const isCI = process.env.CI || process.env.BITBUCKET_BUILD_NUMBER;
const buildNumber = process.env.BITBUCKET_BUILD_NUMBER || 'local';
const branch = process.env.BITBUCKET_BRANCH || 'unknown';
const tag = process.env.BITBUCKET_TAG;
const platform = process.platform;

console.log(`📱 Platform: ${platform}`);
console.log(`🏗️  Build: ${buildNumber}`);
console.log(`🌿 Branch: ${branch}`);
if (tag) console.log(`🏷️  Tag: ${tag}`);
console.log('');

// Create dist directory
const distDir = path.join(__dirname, '..', 'dist');
if (!fs.existsSync(distDir)) {
    fs.mkdirSync(distDir, { recursive: true });
}

// Update version if building from tag
if (tag && tag.startsWith('v')) {
    const version = tag.substring(1); // Remove 'v' prefix
    console.log(`📝 Updating version to ${version}`);
    
    const packagePath = path.join(__dirname, '..', 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    packageJson.version = version;
    fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
}

// Build functions
function buildWindows() {
    console.log('🪟 Building for Windows...');
    try {
        // Set Windows-specific environment
        process.env.CSC_LINK = process.env.WINDOWS_CSC_LINK || '';
        process.env.CSC_KEY_PASSWORD = process.env.WINDOWS_CSC_PASSWORD || '';
        
        execSync('npm run build:win', { stdio: 'inherit' });
        console.log('✅ Windows build completed!');
        
        // List generated files
        const files = fs.readdirSync(distDir).filter(f => 
            f.endsWith('.exe') || f.endsWith('.msi')
        );
        console.log('📦 Generated Windows files:');
        files.forEach(file => {
            const stats = fs.statSync(path.join(distDir, file));
            const sizeMB = (stats.size / 1024 / 1024).toFixed(1);
            console.log(`   - ${file} (${sizeMB} MB)`);
        });
        
    } catch (error) {
        console.error('❌ Windows build failed:', error.message);
        process.exit(1);
    }
}

function buildMac() {
    console.log('🍎 Building for macOS...');
    try {
        // Set macOS-specific environment
        process.env.CSC_LINK = process.env.MACOS_CSC_LINK || '';
        process.env.CSC_KEY_PASSWORD = process.env.MACOS_CSC_PASSWORD || '';
        process.env.APPLE_ID = process.env.APPLE_ID || '';
        process.env.APPLE_ID_PASSWORD = process.env.APPLE_ID_PASSWORD || '';
        
        execSync('npm run build:mac', { stdio: 'inherit' });
        console.log('✅ macOS build completed!');
        
        // List generated files
        const files = fs.readdirSync(distDir).filter(f => 
            f.endsWith('.dmg') || f.endsWith('.zip')
        );
        console.log('📦 Generated macOS files:');
        files.forEach(file => {
            const stats = fs.statSync(path.join(distDir, file));
            const sizeMB = (stats.size / 1024 / 1024).toFixed(1);
            console.log(`   - ${file} (${sizeMB} MB)`);
        });
        
    } catch (error) {
        console.error('❌ macOS build failed:', error.message);
        if (platform !== 'darwin') {
            console.error('💡 Note: macOS builds require macOS runners in Bitbucket Pipelines');
        }
        process.exit(1);
    }
}

function buildLinux() {
    console.log('🐧 Building for Linux...');
    try {
        execSync('npm run build:linux', { stdio: 'inherit' });
        console.log('✅ Linux build completed!');
        
        // List generated files
        const files = fs.readdirSync(distDir).filter(f => 
            f.endsWith('.AppImage') || f.endsWith('.deb') || f.endsWith('.rpm')
        );
        console.log('📦 Generated Linux files:');
        files.forEach(file => {
            const stats = fs.statSync(path.join(distDir, file));
            const sizeMB = (stats.size / 1024 / 1024).toFixed(1);
            console.log(`   - ${file} (${sizeMB} MB)`);
        });
        
    } catch (error) {
        console.error('❌ Linux build failed:', error.message);
        process.exit(1);
    }
}

// Parse command line arguments
const args = process.argv.slice(2);
const target = args[0] || 'current';

console.log(`🎯 Target: ${target}`);
console.log('');

// Build based on target
switch (target) {
    case 'windows':
    case 'win':
        buildWindows();
        break;
        
    case 'mac':
    case 'macos':
        buildMac();
        break;
        
    case 'linux':
        buildLinux();
        break;
        
    case 'all':
        console.log('🔄 Building for all platforms...');
        if (platform === 'win32') {
            buildWindows();
        } else if (platform === 'darwin') {
            buildMac();
        } else {
            buildLinux();
        }
        break;
        
    case 'current':
    default:
        if (platform === 'win32') {
            buildWindows();
        } else if (platform === 'darwin') {
            buildMac();
        } else {
            buildLinux();
        }
        break;
}

// Create build info file
const buildInfo = {
    version: require('../package.json').version,
    buildNumber,
    branch,
    tag,
    platform,
    timestamp: new Date().toISOString(),
    files: fs.readdirSync(distDir)
};

fs.writeFileSync(
    path.join(distDir, 'build-info.json'), 
    JSON.stringify(buildInfo, null, 2)
);

console.log('');
console.log('🎉 Build process completed!');
console.log('📁 Check the dist/ folder for your builds');
console.log('📊 Build info saved to dist/build-info.json');

if (isCI) {
    console.log('');
    console.log('🔗 Artifacts will be available in Bitbucket Downloads');
}

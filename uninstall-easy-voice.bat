@echo off
echo Easy Voice - Manual Uninstall Helper
echo ===================================
echo.
echo This script helps with manual uninstallation of Easy Voice.
echo It will stop processes and remove auto-start configuration.
echo.
echo WARNING: This does NOT uninstall the application files.
echo Use this only if the regular uninstaller is not working.
echo.
set /p confirm="Do you want to continue? (y/N): "
if /i not "%confirm%"=="y" (
    echo Cancelled.
    pause
    exit /b
)

echo.
echo Step 1: Stopping Easy Voice processes...
echo ----------------------------------------
taskkill /F /IM "Easy Voice.exe" /T >nul 2>&1
if %errorlevel%==0 echo - Stopped Easy Voice.exe
taskkill /F /IM "EasyVoice.exe" /T >nul 2>&1  
if %errorlevel%==0 echo - Stopped EasyVoice.exe
taskkill /F /IM "voicea.exe" /T >nul 2>&1
if %errorlevel%==0 echo - Stopped voicea.exe

echo Waiting for processes to terminate...
timeout /t 3 /nobreak >nul

echo.
echo Step 2: Removing auto-start entries...
echo --------------------------------------
reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "Easy Voice" /f >nul 2>&1
if %errorlevel%==0 echo - Removed Easy Voice auto-start (Current User)
reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "EasyVoice" /f >nul 2>&1
if %errorlevel%==0 echo - Removed EasyVoice auto-start (Current User)
reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "VoiceFlow" /f >nul 2>&1
if %errorlevel%==0 echo - Removed VoiceFlow auto-start (Current User)

echo.
echo Step 3: Checking for system-wide auto-start entries...
echo ------------------------------------------------------
reg delete "HKLM\Software\Microsoft\Windows\CurrentVersion\Run" /v "Easy Voice" /f >nul 2>&1
if %errorlevel%==0 echo - Removed Easy Voice auto-start (All Users)
reg delete "HKLM\Software\Microsoft\Windows\CurrentVersion\Run" /v "EasyVoice" /f >nul 2>&1
if %errorlevel%==0 echo - Removed EasyVoice auto-start (All Users)
reg delete "HKLM\Software\Microsoft\Windows\CurrentVersion\Run" /v "VoiceFlow" /f >nul 2>&1
if %errorlevel%==0 echo - Removed VoiceFlow auto-start (All Users)

echo.
echo Step 4: User data cleanup (optional)...
echo ---------------------------------------
set /p cleanup="Do you want to remove user data and settings? (y/N): "
if /i "%cleanup%"=="y" (
    echo Removing user data directories...
    if exist "%APPDATA%\Easy Voice" (
        rmdir /s /q "%APPDATA%\Easy Voice" >nul 2>&1
        echo - Removed %APPDATA%\Easy Voice
    )
    if exist "%APPDATA%\EasyVoice" (
        rmdir /s /q "%APPDATA%\EasyVoice" >nul 2>&1
        echo - Removed %APPDATA%\EasyVoice
    )
    if exist "%APPDATA%\voicea" (
        rmdir /s /q "%APPDATA%\voicea" >nul 2>&1
        echo - Removed %APPDATA%\voicea
    )
) else (
    echo User data preserved.
)

echo.
echo Step 5: Cleanup shortcuts...
echo ----------------------------
if exist "%USERPROFILE%\Desktop\Easy Voice.lnk" (
    del "%USERPROFILE%\Desktop\Easy Voice.lnk" >nul 2>&1
    echo - Removed desktop shortcut
)
if exist "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Easy Voice.lnk" (
    del "%APPDATA%\Microsoft\Windows\Start Menu\Programs\Easy Voice.lnk" >nul 2>&1
    echo - Removed start menu shortcut
)

echo.
echo ========================================
echo Manual cleanup completed!
echo.
echo IMPORTANT: This script only cleaned up processes, auto-start,
echo and user data. To completely remove Easy Voice:
echo.
echo 1. Go to Settings ^> Apps ^& Features
echo 2. Find "Easy Voice" and click Uninstall
echo 3. Or manually delete the installation directory
echo.
echo The application should no longer start automatically
echo or run in the background.
echo ========================================
echo.
pause

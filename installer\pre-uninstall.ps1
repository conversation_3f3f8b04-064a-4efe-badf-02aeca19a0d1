# Pre-uninstall cleanup script for Easy Voice
# This script stops all processes and removes auto-start configuration

Write-Host "Easy Voice - Pre-Uninstall Cleanup" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan
Write-Host ""

# Function to stop processes safely
function Stop-EasyVoiceProcesses {
    Write-Host "Stopping Easy Voice processes..." -ForegroundColor Yellow
    
    $processNames = @("Easy Voice", "EasyVoice", "voicea")
    $stoppedAny = $false
    
    foreach ($processName in $processNames) {
        $processes = Get-Process -Name $processName -ErrorAction SilentlyContinue
        if ($processes) {
            Write-Host "Found $($processes.Count) instance(s) of $processName" -ForegroundColor Yellow
            foreach ($process in $processes) {
                try {
                    $process.CloseMainWindow()
                    Start-Sleep -Seconds 2
                    
                    if (!$process.HasExited) {
                        $process.Kill()
                        Write-Host "Force-stopped process: $($process.ProcessName) (PID: $($process.Id))" -ForegroundColor Red
                    } else {
                        Write-Host "Gracefully stopped process: $($process.ProcessName) (PID: $($process.Id))" -ForegroundColor Green
                    }
                    $stoppedAny = $true
                } catch {
                    Write-Host "Failed to stop process $($process.ProcessName): $($_.Exception.Message)" -ForegroundColor Red
                }
            }
        }
    }
    
    if (!$stoppedAny) {
        Write-Host "No Easy Voice processes found running." -ForegroundColor Green
    }
    
    # Wait a moment for cleanup
    Start-Sleep -Seconds 2
}

# Function to remove auto-start entries
function Remove-AutoStartEntries {
    Write-Host "Removing auto-start configuration..." -ForegroundColor Yellow
    
    $autoStartNames = @("Easy Voice", "EasyVoice", "VoiceFlow")
    $removedAny = $false
    
    # Check current user registry
    $currentUserPath = "HKCU:\Software\Microsoft\Windows\CurrentVersion\Run"
    foreach ($name in $autoStartNames) {
        try {
            $value = Get-ItemProperty -Path $currentUserPath -Name $name -ErrorAction SilentlyContinue
            if ($value) {
                Remove-ItemProperty -Path $currentUserPath -Name $name -Force
                Write-Host "Removed auto-start entry: $name (Current User)" -ForegroundColor Green
                $removedAny = $true
            }
        } catch {
            # Ignore errors - entry might not exist
        }
    }
    
    # Check all users registry (requires admin rights)
    $allUsersPath = "HKLM:\Software\Microsoft\Windows\CurrentVersion\Run"
    foreach ($name in $autoStartNames) {
        try {
            $value = Get-ItemProperty -Path $allUsersPath -Name $name -ErrorAction SilentlyContinue
            if ($value) {
                Remove-ItemProperty -Path $allUsersPath -Name $name -Force
                Write-Host "Removed auto-start entry: $name (All Users)" -ForegroundColor Green
                $removedAny = $true
            }
        } catch {
            # Ignore errors - might not have admin rights or entry doesn't exist
        }
    }
    
    if (!$removedAny) {
        Write-Host "No auto-start entries found." -ForegroundColor Green
    }
}

# Function to clean up user data (optional)
function Remove-UserData {
    Write-Host ""
    $response = Read-Host "Do you want to remove all user data and settings? This cannot be undone. (y/N)"
    
    if ($response -eq 'y' -or $response -eq 'Y') {
        Write-Host "Removing user data..." -ForegroundColor Yellow
        
        $userDataPaths = @(
            "$env:APPDATA\Easy Voice",
            "$env:APPDATA\EasyVoice", 
            "$env:APPDATA\voicea"
        )
        
        foreach ($path in $userDataPaths) {
            if (Test-Path $path) {
                try {
                    Remove-Item -Path $path -Recurse -Force
                    Write-Host "Removed: $path" -ForegroundColor Green
                } catch {
                    Write-Host "Failed to remove: $path - $($_.Exception.Message)" -ForegroundColor Red
                }
            }
        }
    } else {
        Write-Host "User data will be preserved." -ForegroundColor Green
    }
}

# Main execution
try {
    Stop-EasyVoiceProcesses
    Remove-AutoStartEntries
    
    # Only ask about user data if running interactively
    if ([Environment]::UserInteractive) {
        Remove-UserData
    }
    
    Write-Host ""
    Write-Host "Pre-uninstall cleanup completed successfully!" -ForegroundColor Green
    Write-Host "You can now proceed with the uninstallation." -ForegroundColor Green
    
} catch {
    Write-Host ""
    Write-Host "An error occurred during cleanup: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "You may proceed with uninstallation, but some cleanup may be incomplete." -ForegroundColor Yellow
}

Write-Host ""
if ([Environment]::UserInteractive) {
    Read-Host "Press Enter to continue"
}

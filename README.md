# VoiceA - Voice Recording App

A desktop application that allows you to record voice and automatically transcribe it using OpenAI Whisper API. Features global shortcuts and automatic text pasting.

## Features

- **Global Shortcut**: Hold `Ctrl+Shift` to start recording
- **Transparent Widget**: Beautiful recording widget with waveform visualization
- **Auto-Transcription**: Uses OpenAI Whisper API for accurate transcription
- **Auto-Copy**: Automatically copies transcribed text to clipboard
- **Background Operation**: Runs silently in the background
- **Cross-Platform**: Works on Windows, macOS, and Linux

## Installation

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- OpenAI API key

### Setup

1. **Clone or download the project**
   ```bash
   git clone <repository-url>
   cd voicea
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up OpenAI API key**
   
   You need an OpenAI API key to use the transcription feature. You can get one from [OpenAI's website](https://platform.openai.com/api-keys).

   **Option A: Environment Variable (Recommended)**
   ```bash
   # Windows
   set OPENAI_API_KEY=your_api_key_here
   
   # macOS/Linux
   export OPENAI_API_KEY=your_api_key_here
   ```

   **Option B: Create a .env file**
   ```bash
   echo OPENAI_API_KEY=your_api_key_here > .env
   ```

4. **Run the application**
   ```bash
   # Development mode (shows main window)
   npm run dev
   
   # Production mode (runs in background)
   npm start
   ```

## Usage

1. **Start the application**
   - The app runs in the background by default
   - Use `npm run dev` to see the main window for testing

2. **Record voice**
   - Hold `Ctrl+Shift` to start recording
   - A transparent widget appears at the top-right with waveform
   - Speak your message while holding the keys
   - Release `Ctrl+Shift` to stop recording

3. **Automatic processing**
   - Audio is converted to Base64 and sent to main process
   - Saved as temporary file
   - Transcribed using OpenAI Whisper API
   - Result is automatically copied to clipboard

4. **Fallback behavior**
   - If OpenAI API key is not set, shows fallback message
   - Widget auto-hides after recording is completed

## Development

### Project Structure

```
voicea/
├── main.js              # Main Electron process
├── index.html           # Main application window
├── recording-widget.html # Transparent recording widget
├── package.json         # Dependencies and scripts
└── README.md           # This file
```

### Key Components

- **main.js**: Handles global shortcuts, IPC communication, and OpenAI integration
- **index.html**: Main application interface with status and instructions
- **recording-widget.html**: Transparent widget with MediaRecorder and waveform visualization

### Building

To create a distributable application:

```bash
npm run build
```

This will create platform-specific installers in the `dist` folder.

## Troubleshooting

### Common Issues

1. **Microphone access denied**
   - Make sure to allow microphone access when prompted
   - Check system microphone permissions

2. **Global shortcuts not working**
   - On macOS, you may need to grant accessibility permissions
   - On Windows, run as administrator if needed

3. **OpenAI API errors**
   - Verify your API key is correct
   - Check your OpenAI account has sufficient credits
   - Ensure internet connection is available

4. **Text not copying**
   - The app copies text to clipboard automatically
   - Use Ctrl+V to paste the transcribed text manually
   - Check if clipboard access is allowed

### Debug Mode

Run with debug logging:
```bash
npm run dev
```

This shows the main window and console output for troubleshooting.

## Dependencies

- **Electron**: Desktop application framework
- **OpenAI**: API client for Whisper transcription

## License

MIT License - feel free to use and modify as needed.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## Support

If you encounter issues or have questions, please open an issue on the repository. 
Write-Host "VoiceA - OpenAI API Key Setup" -ForegroundColor Green
Write-Host "=============================" -ForegroundColor Green
Write-Host ""
Write-Host "Please enter your OpenAI API key:" -ForegroundColor Yellow
Write-Host "(You can get one from https://platform.openai.com/api-keys)" -ForegroundColor Gray
Write-Host ""

$apiKey = Read-Host "Enter your OpenAI API key"

if ($apiKey) {
    Write-Host ""
    Write-Host "Setting environment variable..." -ForegroundColor Yellow
    $env:OPENAI_API_KEY = $apiKey
    Write-Host ""
    Write-Host "API key set successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "To make this permanent, run this command:" -ForegroundColor Yellow
    Write-Host "[Environment]::SetEnvironmentVariable('OPENAI_API_KEY', '$apiKey', 'User')" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Now you can run: npm run dev" -ForegroundColor Green
} else {
    Write-Host "No API key provided. Skipping..." -ForegroundColor Red
}

Write-Host ""
Read-Host "Press Enter to continue" 
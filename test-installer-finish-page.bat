@echo off
echo Easy Voice - Test Installer Finish Page
echo ======================================
echo.
echo This script helps test the installer finish page functionality.
echo.
echo Current Configuration:
echo - runAfterFinish: true (should show "Run Easy Voice" checkbox)
echo - createDesktopShortcut: true (should create desktop shortcut)
echo - createStartMenuShortcut: true (should create start menu shortcut)
echo.
echo To test:
echo 1. Build the installer: npm run build:win
echo 2. Run the generated installer
echo 3. Complete the installation
echo 4. On the finish page, you should see:
echo    ✅ "Run Easy Voice" checkbox (checked by default)
echo    ✅ "Finish" button
echo 5. If checkbox is checked, Easy Voice should launch after clicking Finish
echo.
echo Expected finish page elements:
echo ┌─────────────────────────────────────────────┐
echo │ Installation Complete                       │
echo │                                             │
echo │ Easy Voice has been successfully installed. │
echo │                                             │
echo │ ☑ Run Easy Voice                           │
echo │                                             │
echo │              [Finish]                       │
echo └─────────────────────────────────────────────┘
echo.
pause

echo.
echo Checking current build configuration...
echo =====================================
echo.
echo Looking for NSIS configuration in package.json:
findstr /C:"runAfterFinish" package.json
findstr /C:"createDesktopShortcut" package.json
findstr /C:"createStartMenuShortcut" package.json

echo.
echo If runAfterFinish shows "true", the finish page should have the Run option.
echo If it shows "false", you won't see the Run checkbox.
echo.
echo To build and test:
echo 1. npm run build:win
echo 2. Run dist\Easy Voice Setup 1.0.0.exe
echo 3. Check the finish page for the Run option
echo.
pause

image: node:18

definitions:
  steps:
    - step: &build-windows
        name: Build for Windows
        runs-on:
          - 'windows'
        script:
          - npm ci
          - npm run build:win
        artifacts:
          - dist/*.exe
          - dist/*.msi
          
    - step: &build-macos
        name: Build for macOS
        runs-on:
          - 'macos'
        script:
          - npm ci
          - npm run build:mac
        artifacts:
          - dist/*.dmg
          - dist/*.zip
          
    - step: &build-linux
        name: Build for Linux
        script:
          - npm ci
          - npm run build:linux
        artifacts:
          - dist/*.AppImage
          - dist/*.deb
          - dist/*.rpm

pipelines:
  # Automatic builds on tags
  tags:
    'v*':
      - parallel:
          - step: *build-windows
          - step: *build-macos
          - step: *build-linux
      - step:
          name: Create Release
          script:
            - echo "Release $BITBUCKET_TAG created"
            - echo "Artifacts available in Downloads section"
          artifacts:
            - dist/**
            
  # Manual builds
  custom:
    build-all:
      - parallel:
          - step: *build-windows
          - step: *build-macos
          - step: *build-linux
    build-windows:
      - step: *build-windows
    build-macos:
      - step: *build-macos
    build-linux:
      - step: *build-linux
      
  # Development builds
  branches:
    main:
      - step:
          name: Test Build
          script:
            - npm ci
            - npm run build:current
          artifacts:
            - dist/**
            
  # Pull request builds
  pull-requests:
    '**':
      - step:
          name: Test Build
          script:
            - npm ci
            - npm test || echo "No tests configured"
            - npm run build:current
          artifacts:
            - dist/**

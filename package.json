{"name": "EasyVoice", "version": "1.0.0", "description": "Voice recording app with global shortcuts and transcription", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "debug": "DEBUG_LOGS=true electron .", "build": "npm run clean:dist && electron-builder", "build:win": "npm run clean:dist && electron-builder --win --publish never", "build:unsigned": "npm run clean:dist && electron-builder --win --publish never --config.win.sign=false", "build:mac": "npm run clean:dist && electron-builder --mac --publish never", "build:linux": "npm run clean:dist && electron-builder --linux --publish never", "build:all": "npm run clean:dist && electron-builder --win --mac --linux --publish never", "build:debug": "npm run clean:dist && electron-builder --win --config.extraMetadata.main=main.js", "dist": "npm run build:win", "pack": "electron-builder --dir", "clean": "rm -rf dist node_modules package-lock.json", "clean:dist": "rmdir /s /q dist 2>nul || echo \"Dist folder cleaned\"", "reinstall": "npm run clean && npm install", "postinstall": "electron-builder install-app-deps"}, "keywords": ["voice", "recording", "transcription", "openai", "whisper", "electron"], "author": {"name": "Easy Voice Inc", "email": "<EMAIL>"}, "license": "MIT", "homepage": "https://easyvoice.com", "repository": {"type": "git", "url": "https://github.com/easyvoice/voicea.git"}, "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1", "electron-rebuild": "^3.2.9"}, "dependencies": {"abort-controller": "^3.0.0", "agentkeepalive": "^4.6.0", "auto-launch": "^5.0.6", "dotenv": "^17.2.0", "formdata-node": "^6.0.3", "ms": "^2.1.3", "node-fetch": "^2.7.0", "node-gyp-build": "^4.8.4", "openai": "^4.104.0", "tr46": "^5.1.1", "uiohook-napi": "^1.5.4", "undici": "^5.26.5", "web-streams-polyfill": "^4.1.0", "webidl-conversions": "^7.0.0", "whatwg-url": "^14.2.0"}, "build": {"appId": "com.easyvoice.app", "productName": "Easy Voice", "directories": {"output": "dist"}, "buildDependenciesFromSource": false, "nodeGypRebuild": false, "files": ["**/*", "node_modules/openai/**/*", "node_modules/uiohook-napi/**/*", "node_modules/node-gyp-build/**/*", "node_modules/dotenv/**/*", "node_modules/node-fetch/**/*", "node_modules/whatwg-url/**/*", "node_modules/webidl-conversions/**/*", "node_modules/tr46/**/*", "node_modules/formdata-node/**/*", "node_modules/undici/**/*", "node_modules/web-streams-polyfill/**/*", "node_modules/agentkeepalive/**/*", "node_modules/humanize-ms/**/*", "node_modules/ms/**/*", "node_modules/abort-controller/**/*", "node_modules/auto-launch/**/*"], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "requestedExecutionLevel": "asInvoker", "sign": null, "verifyUpdateCodeSignature": false}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Easy Voice"}}}